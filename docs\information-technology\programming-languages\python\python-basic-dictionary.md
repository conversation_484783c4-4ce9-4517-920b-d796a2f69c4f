# Python 基础知识字典

本文档面向选考技术学科的高中生，提供 Python 编程常见基础知识点和备考要点，便于快速查阅与考试复习。

## 1. Python 中的常见数据类型
- **整型 (int)**: 例如 `10`, `-3`, `0b101` (二进制), `0o17` (八进制), `0xFF` (十六进制). Python 的整数支持任意精度。
- **浮点型 (float)**: 例如 `3.14`, `-0.5`, `1.2e3` (科学计数法).
- **字符串 (str)**: 例如 `'hello'`, `"world"`, `'''multi-line'''`. 字符串是不可变的 Unicode 字符序列。
- **布尔型 (bool)**: `True`, `False`. 它们是 `int` 的子类，`True` 对应 `1`，`False` 对应 `0`。
- **列表 (list)**: 例如 `[1, 'a', 3.0, True]`. 有序、可变序列。
- **元组 (tuple)**: 例如 `(1, 'a', 3.0, True)`. 有序、不可变序列。
- **字典 (dict)**: 例如 `{'name': 'Alice', 'age': 30}`. 无序（Python 3.6之前）或有序（Python 3.7+）的键值对集合，键必须唯一且不可变，值可变。
- **集合 (set)**: 例如 `{1, 2, 3, 'a'}`. 无序、不重复元素的集合，可变。
- **NoneType**: 特殊类型，只有一个值 `None`，常用于表示缺失或空值。

### 考试提示
- 题目常考列表、字典和字符串的基本操作
- 区分可变与不可变类型
- 了解 `None` 表示空值

## 2. 变量与赋值
- 变量名规则：以字母或下划线开头，后跟字母、数字或下划线。区分大小写。避免使用 Python 关键字和内置函数名作为变量名。
- 赋值操作：使用 `=`
  ```python
  name = "Python"  # 字符串赋值
  age = 30       # 整数赋值
  pi = 3.14159   # 浮点数赋值
  is_learning = True # 布尔值赋值
  my_list = [1, 2, 3] # 列表赋值
  ```
- **多重赋值**:
  ```python
  x, y, z = 1, 2, 3
  a = b = c = 10 # a, b, c 都指向同一个值为10的整数对象
  ```
- **解包赋值**:
  ```python
  coordinates = (10, 20)
  x, y = coordinates # x=10, y=20
  ```
- **交换变量**:
  ```python
  x, y = y, x
  ```

### 考试提示
- 牢记赋值语法 `=` 及多重赋值写法
- 解包和变量交换在选择题中常出现
- 变量名不要使用关键字

## 3. 字符串 (String)
- 创建：单引号 (`'...'`)、双引号 (`"..."`)、三引号 (`'''...'''` 或 `"""..."""` 用于多行字符串或文档字符串)。

- **特性**：不可变 (immutable)。

- **常用操作与方法**：
  - 拼接: `+` (创建新字符串)
  - 重复: `*` (创建新字符串)
  - 索引: `my_string[index]` (负数索引从后向前)
  - 切片: `my_string[start:end:step]` (不包括 `end`)
  - 长度: `len(my_string)`
  - 成员测试: `substring in my_string`
  - 遍历: `for char in my_string:`
  - **格式化**: 
    - f-strings (Python 3.6+): `f"Name: {name}, Age: {age}"` (推荐)
    - `str.format()`: `"Name: {}, Age: {}".format(name, age)`
    - %-formatting (旧式): `"Name: %s, Age: %d" % (name, age)`
  - **查找与替换**:
    - `s.find(sub[, start[, end]])`: 查找子串，返回首次出现的索引，未找到返回 -1。
    - `s.index(sub[, start[, end]])`: 类似 `find`，但未找到会抛出 `ValueError`。
    - `s.replace(old, new[, count])`: 替换子串。
    - `s.count(sub[, start[, end]])`: 统计子串出现次数。
  - **大小写转换**:
    - `s.upper()`: 转大写。
    - `s.lower()`: 转小写。
    - `s.capitalize()`: 首字母大写，其余小写。
    - `s.title()`: 每个单词首字母大写。
  - **判断类方法 (返回布尔值)**:
    - `s.startswith(prefix[, start[, end]])`
    - `s.endswith(suffix[, start[, end]])`
    - `s.isalnum()`: 是否所有字符都是字母或数字。
    - `s.isalpha()`: 是否所有字符都是字母。
    - `s.isdigit()`: 是否所有字符都是数字。
    - `s.islower()`: 是否所有字母字符都是小写。
    - `s.isupper()`: 是否所有字母字符都是大写。
    - `s.isspace()`: 是否所有字符都是空白字符。
  - **去除空白**:
    - `s.strip([chars])`: 去除两端指定字符 (默认空白)。
    - `s.lstrip([chars])`: 去除左侧。
    - `s.rstrip([chars])`: 去除右侧。
  - **分割与连接**:
    - `s.split(sep=None, maxsplit=-1)`: 按分隔符分割字符串成列表。
    - `sep.join(iterable)`: 用 `sep` 连接可迭代对象中的字符串。

### 考试提示
- 常考切片、拼接和格式化字符串
- 熟悉查找与替换相关方法
- 输入读取时记得转换数据类型

## 4. 列表 (List)
- 创建：`[]`, `list()`, `[x for x in iterable]` (列表推导式)。

- **特性**：有序 (ordered)、可变 (mutable)、可包含不同类型元素。

- **常用操作与方法**：
  - 索引: `my_list[index]`
  - 切片: `my_list[start:end:step]` (返回新列表)
  - 长度: `len(my_list)`
  - 拼接: `+` (创建新列表)
  - 重复: `*` (创建新列表)
  - 成员测试: `item in my_list`
  - 遍历: `for item in my_list:`
  - **添加元素**:
    - `l.append(x)`: 在末尾添加。
    - `l.insert(i, x)`: 在指定索引 `i` 处插入。
  - **删除元素**:
    - `l.pop([i])`: 删除并返回指定索引 `i` 处的元素 (默认末尾)。
    - `l.remove(x)`: 删除第一个值为 `x` 的元素 (未找到抛出 `ValueError`)。
    - `del my_list[i]`: 删除指定索引处的元素。
  - **查找与计数**:
    - `l.index(x[, start[, end]])`: 返回第一个值为 `x` 的元素的索引 (未找到抛出 `ValueError`)。
    - `l.count(x)`: 统计 `x` 出现的次数。
  - **排序与反转**:
    - `l.sort(key=None, reverse=False)`: 原地排序。
    - `sorted(iterable, key=None, reverse=False)`: 返回新的已排序列表 (内置函数)。
    - `l.reverse()`: 原地反转列表。
  - **复制**:
    - `new_list = old_list[:]` (浅拷贝)

### 考试提示
- 熟练掌握索引、切片及添加删除元素的方法
- 了解列表复制的基本方法
- 排序与遍历往往是常见考题

## 5. 元组 (Tuple)
- 创建：`()`, `tuple()`, `(item,)` (单个元素的元组需要逗号)。

- **特性**：有序 (ordered)、不可变 (immutable)。通常用于存储异构数据集合，或作为字典的键。

- **常用操作**：
  - 索引: `my_tuple[index]`
  - 切片: `my_tuple[start:end:step]` (返回新元组)
  - 长度: `len(my_tuple)`
  - 拼接: `+` (创建新元组)
  - 重复: `*` (创建新元组)
  - 成员测试: `item in my_tuple`
  - 遍历: `for item in my_tuple:`
- `t.count(x)`: 统计 `x` 出现的次数。
- `t.index(x[, start[, end]])`: 返回第一个值为 `x` 的元素的索引。
- **用途**：函数返回多个值时自动打包成元组，字符串格式化，用作字典键等。

### 考试提示
- 单元素元组记得加逗号 `(item,)`
- 元组不可变，常用来保存固定数据或作为字典键

## 6. 字典 (Dictionary)
- 创建：`{}`, `dict()`, `dict(key1=value1, key2=value2)`.

- **特性**：键值对 (key-value pairs) 集合。键必须是唯一的且不可变类型 (如字符串、数字、元组)。值可以是任意类型。Python 3.7+ 版本字典保持插入顺序。

- **常用操作与方法**：
  - 访问值: `my_dict[key]` (若键不存在，抛出 `KeyError`)。
  - `d.get(key[, default])`: 安全访问，若键不存在，返回 `default` (默认为 `None`)。
  - 添加/修改键值对: `my_dict[key] = value`
  - 删除键值对:
    - `d.pop(key[, default])`: 删除并返回指定键的值 (若键不存在且未提供 `default`，抛出 `KeyError`)。
    - `del my_dict[key]`: 删除指定键的项。
  - 长度: `len(my_dict)` (键值对数量)
  - 成员测试: `key in my_dict` (检查键是否存在)
  - **视图对象 (动态)**:
    - `d.keys()`: 返回包含所有键的视图。
    - `d.values()`: 返回包含所有值的视图。
    - `d.items()`: 返回包含所有 (键, 值) 对的视图。
  - 遍历:
    ```python
    for key in my_dict: # 遍历键
        print(key, my_dict[key])
    for value in my_dict.values(): # 遍历值
        print(value)
    for key, value in my_dict.items(): # 遍历键值对
        print(key, value)
    ```
  - `d.update(other_dict)`: 用 `other_dict` 中的键值对更新 `d`。

### 考试提示
- 常考遍历字典和使用 `get()` 方法
- 理解键必须唯一且不可变

## 7. 集合 (Set)
- 创建：`{1, 2, 3}`, `set()`, `set(iterable)`。注意：`{}` 创建的是空字典，空集合必须用 `set()`。

- **特性**：无序 (unordered)、可变 (mutable)、不包含重复元素。

- **常用操作与方法**：
  - 长度: `len(my_set)`
  - 成员测试: `item in my_set` (高效)
  - 遍历: `for item in my_set:`
  - **添加元素**:
    - `s.add(elem)`: 添加单个元素。
    - `s.update(iterable)`: 添加多个元素 (可以是另一个集合、列表、元组等)。
  - **删除元素**:
    - `s.remove(elem)`: 删除元素 (若元素不存在，抛出 `KeyError`)。
    - `s.discard(elem)`: 删除元素 (若元素不存在，不做任何事)。
  - **集合运算**:
    - 并集: `s1 | s2` 或 `s1.union(s2)`
    - 交集: `s1 & s2` 或 `s1.intersection(s2)`
    - 差集: `s1 - s2` 或 `s1.difference(s2)`
  - **复制**: `new_set = old_set.copy()` (浅拷贝)

### 考试提示
- 集合不允许重复元素
- 常见考题涉及并集、交集和差集运算

## 8. 输入输出
- **输入**: `variable = input("提示信息: ")`
  - `input()` 函数总是返回字符串类型。需要使用 `int()`, `float()` 等进行类型转换。
  ```python
  name = input("Enter your name: ")
  age_str = input("Enter your age: ")
  age_int = int(age_str)
  ```
- **输出**: `print(value1, value2, ..., sep=' ', end='\n', file=sys.stdout, flush=False)`
  - `sep`: 分隔符，默认为空格。
  - `end`: 结束符，默认为换行符 `\n`。
  - `file`: 输出目标，默认为标准输出。
  - `flush`: 是否立即刷新缓冲区。
  ```python
  print("Hello", "World", sep="-") # Hello-World
  print("First line", end="...")
  print("Second line") # First line...Second line
  ```

### 考试提示
- `input()` 得到的都是字符串，必要时转换为数字
- `print()` 可以同时输出多个变量

## 9. 常见内置函数 (部分)
- `abs(x)`: 返回数字的绝对值。
- `bin(x)`: 将整数转换为二进制字符串 (以 `0b` 开头)。
- `bool(x)`: 将值转换为布尔值。
- `chr(i)`: 返回整数 `i` 对应的 Unicode 字符。
- `ord(c)`: 返回字符 `c` 对应的 Unicode 码点 (整数)。
- `dict()`: 创建字典。
- `divmod(a, b)`: 返回 `(a // b, a % b)`。
- `enumerate(iterable, start=0)`: 返回一个枚举对象，产生 (索引, 值) 对。
- `float()`: 转换为浮点数。
- `hex(x)`: 将整数转换为十六进制字符串 (以 `0x` 开头)。
- `input()`: 获取用户输入。
- `int()`: 转换为整数。
- `isinstance(object, classinfo)`: 判断对象是否是指定类或类型的实例。
- `len(s)`: 返回对象的长度。
- `list()`: 创建列表。
- `max(iterable, *[, key, default])` 或 `max(arg1, arg2, *args[, key])`: 返回最大值。
- `min(iterable, *[, key, default])` 或 `min(arg1, arg2, *args[, key])`: 返回最小值。
- `oct(x)`: 将整数转换为八进制字符串 (以 `0o` 开头)。
- `open(file, mode='r', ...)`: 打开文件并返回文件对象。
- `pow(base, exp[, mod])`: 计算 `base` 的 `exp` 次方，可选模 `mod`。
- `print()`: 打印输出。
- `range(stop)` 或 `range(start, stop[, step])`: 生成数字序列。
- `reversed(seq)`: 返回反向迭代器。
- `round(number[, ndigits])`: 四舍五入到指定小数位数。
- `set()`: 创建集合。
- `sorted(iterable, *, key=None, reverse=False)`: 返回新的已排序列表。
- `str()`: 转换为字符串。
- `sum(iterable, /, start=0)`: 求和。
- `tuple()`: 创建元组。
- `type(object)` 或 `type(name, bases, dict)`: 返回对象类型或创建新类型。

### 考试提示
- 熟记 `len()`, `range()`, `type()` 等常用函数的作用

## 10. 运算符 (Operators)
- **算术运算符**: `+`, `-`, `*`, `/` (浮点除), `//` (整数除), `%` (取模), `**` (幂)。
- **比较运算符**: `==`, `!=`, `>`, `<`, `>=`, `<=`。
- **赋值运算符**: `=`, `+=`, `-=`, `*=`, `/=`, `//=`, `%=`, `**=`。
- **逻辑运算符**: `and`, `or`, `not`。
- **成员运算符**: `in`, `not in`。
- **运算符优先级**: 算术运算符 > 比较运算符 > 逻辑运算符。`**` 优先级最高，`not` > `and` > `or`。
  建议使用括号明确运算顺序。
### 考试提示
- 关注算术、比较和逻辑运算符的用法
- 记住 `and`、`or`、`not` 的短路特性

## 11. 分支结构 (Conditional Statements)
- `if` 语句:
  ```python
  if condition1:
      # code block 1 (executes if condition1 is True)
  elif condition2: # optional
      # code block 2 (executes if condition1 is False and condition2 is True)
  else: # optional
      # code block 3 (executes if all preceding conditions are False)
  ```
### 考试提示
- `if/elif/else` 结构是选择题和填空题的常客

## 12. 循环结构 (Loops)
- `for` 循环 (遍历序列或可迭代对象):
  ```python
  for item in iterable:
      # code block for each item
  ```
  ```python
  for i in range(5): # iterates from 0 to 4
      print(i)
  ```
- `while` 循环 (条件为真时执行):
  ```python
  while condition:
      # code block
  ```
### 考试提示
- 掌握 for 和 while 的基本语法
- 注意循环的终止条件避免死循环

## 13. 转移和中断结构 (Control Flow Statements)
- `break`: 立即终止当前最内层的 `for` 或 `while` 循环。
- `continue`: 跳过当前循环的剩余部分，直接进入下一次迭代。
- `pass`: 空语句，用作占位符，当语法上需要一个语句但程序不需要执行任何操作时使用。
### 考试提示
- break 和 continue 常用于提前结束循环或跳过本次循环
- pass 在编写占位代码时使用

## 14. 模块的导入 (Importing Modules)
- `import module_name`
  - 使用: `module_name.function_name()`
- `from module_name import specific_item1, specific_item2`
  - 使用: `specific_item1()`

### 考试提示
- 掌握基本的 import 语法

## 15. 自定义函数 (Defining Functions)
- 定义函数:
  ```python
  def function_name(param1, param2, ..., paramN=default_value):
      """Docstring: 描述函数功能、参数和返回值."""
      # code block
      return value # 可选，若无 return 或 return 后无值，则返回 None
  ```
- 调用函数:
  ```python
  result = function_name(arg1, arg2)
  ```
- **参数类型**：
  - **位置参数 (Positional arguments)**: 按顺序传递。
  - **默认参数值 (Default argument values)**: 定义时指定默认值，调用时可省略。

### 考试提示
- 理解函数的定义和调用格式
- 注意默认参数的使用

## 16. 异常处理 (Exception Handling)
- `try...except...finally` 块:
  ```python
  try:
      # 可能引发异常的代码块
      result = 10 / 0
  except ZeroDivisionError as e: # 捕获特定异常
      print(f"Error: Cannot divide by zero. ({e})")
  except Exception as e: # 捕获所有其他 Exception 子类的异常 (通用)
      print(f"An unexpected error occurred: {e}")
  finally: # 可选，无论是否发生异常都会执行 (通常用于资源清理)
      print("Execution finished.")
  ```
### 考试提示
- 掌握 try/except 基本结构
- 会使用 finally 进行资源清理

## 17. 文件操作 (File I/O)
- **打开文件**: `file_object = open(filename, mode='r', encoding=None)`
  - `mode`: `'r'` (读), `'w'` (写，覆盖), `'a'` (追加)。
  - `encoding`: 文件编码，如 `'utf-8'`。
- **读取文件**:
  - `file.read([size])`: 读取指定字节数或整个文件。
  - `file.readline([size])`: 读取一行。
  - 迭代文件对象: `for line in file_object:`
- **写入文件**:
  - `file.write(string)`
- **关闭文件**: `file_object.close()` (重要！释放资源)
- **使用 `with` 语句 (推荐，自动关闭文件)**:
  ```python
  with open("myfile.txt", "r", encoding="utf-8") as f:
      content = f.read()
      # ... process content ...
  # 文件在此处自动关闭
  ```

### 考试提示
- 文件操作常与 with 语句结合考察
- 牢记常用模式: r/w/a
- 文件使用后记得关闭或使用 with 自动管理

## 18. 常用标准库模块 (部分)
- `math`: 数学函数 (`sqrt`, `pi`, `e` 等)。
- `random`: 生成伪随机数 (`random`, `randint`, `choice` 等)。
### 考试提示
- 了解 `math` 和 `random` 模块的基本用途即可

